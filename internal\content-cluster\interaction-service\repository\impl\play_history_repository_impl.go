package impl

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	"pxpat-backend/pkg/ksuid"
)

type PlayHistoryRepositoryImpl struct {
	db *gorm.DB
}

func NewPlayHistoryRepository(db *gorm.DB) repository.PlayHistoryRepository {
	return &PlayHistoryRepositoryImpl{
		db: db,
	}
}

// UpdatePlayHistory 更新播放历史记录，如果不存在则创建（使用PostgreSQL的UPSERT）
func (r *PlayHistoryRepositoryImpl) UpdatePlayHistory(ctx context.Context, userKSUID, contentKSUID, contentType string, playDuration int64) (*model.PlayHistoryItem, bool, error) {
	tableName := model.GetPlayHistoryItemTableName(userKSUID)

	// 使用PostgreSQL的ON CONFLICT语句实现UPSERT
	sql := fmt.Sprintf(`
		INSERT INTO %s (play_history_item_id, user_ksuid, content_ksuid, content_type, play_duration, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, NOW(), NOW())
		ON CONFLICT (user_ksuid, content_ksuid)
		DO UPDATE SET
			content_type = EXCLUDED.content_type,
			play_duration = EXCLUDED.play_duration,
			updated_at = NOW()
		RETURNING play_history_item_id, user_ksuid, content_ksuid, content_type, play_duration, created_at, updated_at,
			(xmax = 0) AS is_new
	`, tableName)

	var playHistory model.PlayHistoryItem
	var isNew bool
	var createdAt, updatedAt time.Time

	// 生成新的KSUID（如果是插入操作会使用，如果是更新操作会被忽略）
	newKSUID := ksuid.GenerateKSUID()

	err := r.db.WithContext(ctx).Raw(sql, newKSUID, userKSUID, contentKSUID, contentType, playDuration).
		Row().Scan(
		&playHistory.PlayHistoryItemID,
		&playHistory.UserKSUID,
		&playHistory.ContentKSUID,
		&playHistory.ContentType,
		&playHistory.PlayDuration,
		&createdAt,
		&updatedAt,
		&isNew,
	)

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("content_type", contentType).
			Int64("play_duration", playDuration).
			Msg("UPSERT播放历史记录失败")
		return nil, false, err
	}

	playHistory.CreatedAt = createdAt
	playHistory.UpdatedAt = updatedAt

	if isNew {
		log.Info().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("content_type", contentType).
			Int64("play_duration", playDuration).
			Str("play_history_item_id", playHistory.PlayHistoryItemID).
			Msg("创建播放历史记录成功")
	} else {
		log.Info().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Str("content_type", contentType).
			Int64("play_duration", playDuration).
			Str("play_history_item_id", playHistory.PlayHistoryItemID).
			Msg("更新播放历史记录成功")
	}

	return &playHistory, isNew, nil
}

// GetByID 根据ID获取播放历史项
func (r *PlayHistoryRepositoryImpl) GetByID(ctx context.Context, playHistoryItemID string, userKSUID string) (*model.PlayHistoryItem, error) {
	var playHistory model.PlayHistoryItem
	playHistory.UserKSUID = userKSUID // 设置UserKSUID以确定分表

	result := r.db.WithContext(ctx).Table(playHistory.TableName()).
		Where("play_history_item_id = ? AND user_ksuid = ?", playHistoryItemID, userKSUID).
		First(&playHistory)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // 记录不存在，返回nil而不是错误
		}
		log.Error().
			Err(result.Error).
			Str("play_history_item_id", playHistoryItemID).
			Str("user_ksuid", userKSUID).
			Msg("查询播放历史记录失败")
		return nil, result.Error
	}

	return &playHistory, nil
}

// GetByUserAndContent 根据用户和内容获取播放历史项
func (r *PlayHistoryRepositoryImpl) GetByUserAndContent(ctx context.Context, userKSUID, contentKSUID string) (*model.PlayHistoryItem, error) {
	var playHistory model.PlayHistoryItem
	playHistory.UserKSUID = userKSUID // 设置UserKSUID以确定分表

	result := r.db.WithContext(ctx).Table(playHistory.TableName()).
		Where("user_ksuid = ? AND content_ksuid = ?", userKSUID, contentKSUID).
		First(&playHistory)

	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, nil // 记录不存在，返回nil而不是错误
		}
		log.Error().
			Err(result.Error).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Msg("查询播放历史记录失败")
		return nil, result.Error
	}

	return &playHistory, nil
}

// GetUserPlayHistories 获取用户播放历史记录（分页，支持内容类型过滤）
func (r *PlayHistoryRepositoryImpl) GetUserPlayHistories(ctx context.Context, userKSUID string, page, pageSize int, contentType string) ([]*model.PlayHistoryItem, int64, error) {
	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("content_type", contentType).
		Msg("开始获取用户播放历史记录")

	var playHistories []*model.PlayHistoryItem
	var total int64

	// 创建临时对象以获取表名
	tempPlayHistory := &model.PlayHistoryItem{UserKSUID: userKSUID}
	tableName := tempPlayHistory.TableName()

	// 构建查询
	query := r.db.WithContext(ctx).Table(tableName).
		Where("user_ksuid = ?", userKSUID)

	// 如果指定了内容类型，添加类型过滤
	if contentType != "" {
		query = query.Where("content_type = ?", contentType)
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", page).
			Int("page_size", pageSize).
			Str("content_type", contentType).
			Msg("获取用户播放历史记录总数失败")
		return nil, 0, err
	}

	// 获取分页数据，按更新时间倒序
	offset := (page - 1) * pageSize
	if err := query.
		Order("updated_at DESC").
		Limit(pageSize).
		Offset(offset).
		Find(&playHistories).Error; err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("page", page).
			Int("page_size", pageSize).
			Str("content_type", contentType).
			Msg("获取用户播放历史记录失败")
		return nil, 0, err
	}

	log.Debug().
		Str("user_ksuid", userKSUID).
		Int("page", page).
		Int("page_size", pageSize).
		Str("content_type", contentType).
		Int64("total", total).
		Int("count", len(playHistories)).
		Msg("获取用户播放历史记录成功")

	return playHistories, total, nil
}

// DeleteByIDs 根据ID数组删除用户播放历史记录（软删除）
func (r *PlayHistoryRepositoryImpl) DeleteByIDs(ctx context.Context, userKSUID string, playHistoryItemIDs []string) (int64, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Interface("play_history_item_ids", playHistoryItemIDs).
		Msg("开始删除指定的用户播放历史记录")

	// 创建临时对象以获取表名
	tempPlayHistory := &model.PlayHistoryItem{UserKSUID: userKSUID}
	tableName := tempPlayHistory.TableName()

	// 执行软删除
	result := r.db.WithContext(ctx).Table(tableName).
		Where("user_ksuid = ? AND play_history_item_id IN ? AND deleted_at IS NULL", userKSUID, playHistoryItemIDs).
		Update("deleted_at", time.Now())

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("user_ksuid", userKSUID).
			Interface("play_history_item_ids", playHistoryItemIDs).
			Msg("删除指定的用户播放历史记录失败")
		return 0, result.Error
	}

	deletedCount := result.RowsAffected

	log.Info().
		Str("user_ksuid", userKSUID).
		Interface("play_history_item_ids", playHistoryItemIDs).
		Int64("deleted_count", deletedCount).
		Msg("删除指定的用户播放历史记录成功")

	return deletedCount, nil
}

// ClearAllUserPlayHistories 清空用户播放历史记录（软删除）
func (r *PlayHistoryRepositoryImpl) ClearAllUserPlayHistories(ctx context.Context, userKSUID string) (int64, error) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Msg("开始清空用户播放历史记录")

	// 创建临时对象以获取表名
	tempPlayHistory := &model.PlayHistoryItem{UserKSUID: userKSUID}
	tableName := tempPlayHistory.TableName()

	// 使用软删除，设置deleted_at字段
	result := r.db.WithContext(ctx).Table(tableName).
		Where("user_ksuid = ? AND deleted_at IS NULL", userKSUID).
		Update("deleted_at", time.Now())

	if result.Error != nil {
		log.Error().
			Err(result.Error).
			Str("user_ksuid", userKSUID).
			Msg("清空用户播放历史记录失败")
		return 0, result.Error
	}

	clearedCount := result.RowsAffected

	log.Info().
		Str("user_ksuid", userKSUID).
		Int64("cleared_count", clearedCount).
		Msg("清空用户播放历史记录成功")

	return clearedCount, nil
}

// CountByUser 统计用户的播放历史项总数
func (r *PlayHistoryRepositoryImpl) CountByUser(ctx context.Context, userKSUID string) (int64, error) {
	var count int64

	// 创建临时对象以获取表名
	tempPlayHistory := &model.PlayHistoryItem{UserKSUID: userKSUID}
	tableName := tempPlayHistory.TableName()

	err := r.db.WithContext(ctx).Table(tableName).
		Where("user_ksuid = ?", userKSUID).
		Count(&count).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("统计用户播放历史项总数失败")
		return 0, err
	}

	return count, nil
}

// CountByContentType 按内容类型统计播放历史项数量
func (r *PlayHistoryRepositoryImpl) CountByContentType(ctx context.Context, userKSUID string, contentType string) (int64, error) {
	var count int64

	// 创建临时对象以获取表名
	tempPlayHistory := &model.PlayHistoryItem{UserKSUID: userKSUID}
	tableName := tempPlayHistory.TableName()

	err := r.db.WithContext(ctx).Table(tableName).
		Where("user_ksuid = ? AND content_type = ?", userKSUID, contentType).
		Count(&count).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_type", contentType).
			Msg("按内容类型统计播放历史项数量失败")
		return 0, err
	}

	return count, nil
}

// GetRecentPlayHistories 获取用户最近播放的内容
func (r *PlayHistoryRepositoryImpl) GetRecentPlayHistories(ctx context.Context, userKSUID string, limit int) ([]*model.PlayHistoryItem, error) {
	var playHistories []*model.PlayHistoryItem

	// 创建临时对象以获取表名
	tempPlayHistory := &model.PlayHistoryItem{UserKSUID: userKSUID}
	tableName := tempPlayHistory.TableName()

	err := r.db.WithContext(ctx).Table(tableName).
		Where("user_ksuid = ?", userKSUID).
		Order("updated_at DESC").
		Limit(limit).
		Find(&playHistories).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Int("limit", limit).
			Msg("获取用户最近播放的内容失败")
		return nil, err
	}

	return playHistories, nil
}

// ExistsByUserAndContent 检查用户是否有指定内容的播放历史
func (r *PlayHistoryRepositoryImpl) ExistsByUserAndContent(ctx context.Context, userKSUID, contentKSUID string) (bool, error) {
	var count int64

	// 创建临时对象以获取表名
	tempPlayHistory := &model.PlayHistoryItem{UserKSUID: userKSUID}
	tableName := tempPlayHistory.TableName()

	err := r.db.WithContext(ctx).Table(tableName).
		Where("user_ksuid = ? AND content_ksuid = ?", userKSUID, contentKSUID).
		Count(&count).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", contentKSUID).
			Msg("检查播放历史是否存在失败")
		return false, err
	}

	return count > 0, nil
}

// BatchGetPlayHistories 批量获取播放历史状态
func (r *PlayHistoryRepositoryImpl) BatchGetPlayHistories(ctx context.Context, userKSUID string, contentKSUIDs []string) (map[string]*model.PlayHistoryItem, error) {
	if len(contentKSUIDs) == 0 {
		return make(map[string]*model.PlayHistoryItem), nil
	}

	var playHistories []*model.PlayHistoryItem

	// 创建临时对象以获取表名
	tempPlayHistory := &model.PlayHistoryItem{UserKSUID: userKSUID}
	tableName := tempPlayHistory.TableName()

	err := r.db.WithContext(ctx).Table(tableName).
		Where("user_ksuid = ? AND content_ksuid IN ?", userKSUID, contentKSUIDs).
		Find(&playHistories).Error

	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Interface("content_ksuids", contentKSUIDs).
			Msg("批量获取播放历史状态失败")
		return nil, err
	}

	// 转换为map
	result := make(map[string]*model.PlayHistoryItem)
	for _, playHistory := range playHistories {
		result[playHistory.ContentKSUID] = playHistory
	}

	return result, nil
}

// GetUserPlayHistoryStats 获取用户播放历史统计信息
func (r *PlayHistoryRepositoryImpl) GetUserPlayHistoryStats(ctx context.Context, userKSUID string) (map[string]int64, error) {
	stats := make(map[string]int64)

	// 统计总数
	totalCount, err := r.CountByUser(ctx, userKSUID)
	if err != nil {
		log.Error().
			Err(err).
			Str("user_ksuid", userKSUID).
			Msg("获取用户播放历史总数失败")
		return nil, err
	}
	stats["total"] = totalCount

	// 统计各内容类型数量
	contentTypes := []string{"video", "anime", "short"}
	for _, contentType := range contentTypes {
		count, err := r.CountByContentType(ctx, userKSUID, contentType)
		if err != nil {
			log.Error().
				Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_type", contentType).
				Msg("获取用户播放历史内容类型统计失败")
			return nil, err
		}
		stats[contentType] = count
	}

	return stats, nil
}
