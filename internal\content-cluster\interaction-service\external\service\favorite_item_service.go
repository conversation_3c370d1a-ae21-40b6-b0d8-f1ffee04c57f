package service

import (
	"context"
	"fmt"
	"math"
	"pxpat-backend/pkg/errors"

	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/client"
	"pxpat-backend/internal/content-cluster/interaction-service/dto"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	"pxpat-backend/internal/content-cluster/interaction-service/repository"
	repositoryErrors "pxpat-backend/pkg/errors/repository"
)

// FavoriteItemService 收藏项服务实现
type FavoriteItemService struct {
	favoriteFolderRepo repository.FavoriteFolderRepository
	favoriteItemRepo   repository.FavoriteItemRepository
	userClient         client.UserServiceClient
	videoClient        client.VideoServiceClient
	novelClient        client.NovelServiceClient
	musicClient        client.MusicServiceClient
}

// NewFavoriteItemService 创建收藏项服务实例
func NewFavoriteItemService(
	favoriteFolderRepo repository.FavoriteFolderRepository,
	favoriteItemRepo repository.FavoriteItemRepository,
	userClient client.UserServiceClient,
	videoClient client.VideoServiceClient,
	novelClient client.NovelServiceClient,
	musicClient client.MusicServiceClient,
) *FavoriteItemService {
	return &FavoriteItemService{
		favoriteFolderRepo: favoriteFolderRepo,
		favoriteItemRepo:   favoriteItemRepo,
		userClient:         userClient,
		videoClient:        videoClient,
		novelClient:        novelClient,
		musicClient:        musicClient,
	}
}

// getContentInfoByType 根据内容类型从相应的服务获取内容信息
func (s *FavoriteItemService) getContentInfoByType(contentKSUID, contentType string) (*client.ContentInfo, *errors.Errors) {
	switch contentType {
	case "video", "anime", "short":
		// 视频、动漫、短视频都通过video服务处理
		content, err := s.videoClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	case "novel":
		// 小说通过novel服务处理
		content, err := s.novelClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	case "music":
		// 音乐通过music服务处理
		content, err := s.musicClient.GetContentInfo(contentKSUID)
		if err != nil {
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
		}
		return content, nil
	default:
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("不支持的内容类型: %s", contentType))
	}
}

// AddToFavorite 添加到收藏夹
func (s *FavoriteItemService) AddToFavorite(ctx context.Context, userKSUID string, req *dto.AddToFavoriteRequest) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Str("content_type", req.ContentType).
		Strs("folder_ids", req.FavoriteFolderIDs).
		Msg("正在添加内容到收藏夹")

	// 根据内容类型验证内容是否存在
	content, gErr := s.getContentInfoByType(req.ContentKSUID, req.ContentType)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("content_ksuid", req.ContentKSUID).
			Str("content_type", req.ContentType).
			Msg("内容不存在")
		return gErr
	}

	// 验证内容类型
	contentType := model.ContentType(req.ContentType)
	if !model.IsValidContentType(contentType) {
		if content != nil && content.ContentType != "" {
			contentType = model.ContentType(content.ContentType) // 使用内容服务返回的类型
		} else {
			return errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, repositoryErrors.ErrInvalidContentType)
		}
	}

	// 如果没有指定收藏夹，添加到默认收藏夹
	folderIDs := req.FavoriteFolderIDs
	if len(folderIDs) == 0 {
		folderIDs = []string{""} // 空字符串表示使用默认收藏夹
	}

	// 检查重复收藏
	nonDuplicateFolderIDs, err := s.checkDuplicateFavorites(ctx, userKSUID, req.ContentKSUID, folderIDs)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("检查重复收藏失败")
		return err
	}

	if len(nonDuplicateFolderIDs) == 0 {
		// 所有收藏夹都已存在该内容
		log.Info().
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内容已在所有指定收藏夹中存在")
		return nil
	}

	// 批量添加到收藏夹
	for _, folderID := range nonDuplicateFolderIDs {
		// 创建收藏项
		item := model.NewFavoriteItem(userKSUID, req.ContentKSUID, model.ContentType(req.ContentType), folderID)
		err := s.favoriteItemRepo.BatchCreate(ctx, []*model.FavoriteItem{item})
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("content_ksuid", req.ContentKSUID).
				Str("folder_id", folderID).
				Msg("添加到收藏夹失败")
			return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.CREATE_RECORD_ERROR, err)
		}
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Int("added_count", len(nonDuplicateFolderIDs)).
		Msg("内容添加到收藏夹成功")

	return nil
}

// RemoveFromFavorite 从收藏夹移除
func (s *FavoriteItemService) RemoveFromFavorite(ctx context.Context, userKSUID string, req *dto.RemoveFromFavoriteRequest) *errors.Errors {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("正在从收藏夹移除内容")

	// 从收藏中移除内容
	err := s.favoriteItemRepo.DeleteByContentKSUID(ctx, userKSUID, req.ContentKSUID)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("从收藏夹移除内容失败")
		return errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.DELETE_RECORD_ERROR, err)
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("内容从收藏夹移除成功")

	return nil
}

// MoveFavoriteItem 移动收藏项
func (s *FavoriteItemService) MoveFavoriteItem(ctx context.Context, userKSUID string, req *dto.MoveFavoriteItemRequest) (*dto.BatchFavoriteOperationResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("item_id", req.FavoriteItemID).
		Str("target_folder_id", req.TargetFolderID).
		Msg("正在移动收藏项")

	// 移动单个收藏项
	if req.FavoriteItemID != "" {
		err := s.favoriteItemRepo.MoveTo(ctx, req.FavoriteItemID, userKSUID, req.TargetFolderID)
		if err != nil {
			log.Error().Err(err).
				Str("user_ksuid", userKSUID).
				Str("item_id", req.FavoriteItemID).
				Msg("移动收藏项失败")
			return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.UPDATE_RECORD_ERROR, err)
		}

		return &dto.BatchFavoriteOperationResponse{
			Success:        true,
			TotalRequested: 1,
			SuccessCount:   1,
			SkippedCount:   0,
		}, nil
	}

	// 批量移动收藏项
	if len(req.TargetFolderIDs) > 0 {
		// 这里需要实现批量移动逻辑
		// 暂时返回错误，需要扩展repository接口
		return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("batch move not implemented yet"))
	}

	return nil, errors.NewGlobalErrors(errors.INVALID_PARAMETER, errors.INVALID_PARAMETER, fmt.Errorf("no items specified for move operation"))
}

// GetFavoriteItems 获取收藏项列表
func (s *FavoriteItemService) GetFavoriteItems(ctx context.Context, currentUserKSUID string, req *dto.GetFavoriteItemsRequest) (*dto.GetFavoriteItemsResponse, *errors.Errors) {
	log.Info().
		Str("current_user_ksuid", currentUserKSUID).
		Str("folder_id", req.FavoriteFolderID).
		Str("content_type", req.ContentType).
		Int("page", req.Page).
		Int("page_size", req.PageSize).
		Msg("正在获取收藏项列表")

	var items []*model.FavoriteItem
	var total int64
	var err error

	folderInfo, err := s.favoriteFolderRepo.GetByFolderKSUID(ctx, req.FavoriteFolderID)
	if err != nil {
		log.Error().Err(err).
			Str("current_user_ksuid", currentUserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Str("content_type", req.ContentType).
			Int("page", req.Page).
			Int("page_size", req.PageSize).
			Msg("获取收藏项列表失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	if !folderInfo.IsPublic && folderInfo.UserKSUID != currentUserKSUID {
		log.Error().
			Str("current_user_ksuid", currentUserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Str("content_type", req.ContentType).
			Msg("BYD,想看别人私有收藏夹")
		return nil, errors.NewGlobalErrors(errors.PERMISSION_DENIED, errors.PERMISSION_DENIED, fmt.Errorf("permission denied"))
	}

	// 根据查询条件获取收藏项
	if req.ContentType != "" {
		// 按收藏夹和内容类型查询
		items, total, err = s.favoriteItemRepo.GetByFolderAndContentType(ctx, folderInfo.UserKSUID, req.FavoriteFolderID, model.ContentType(req.ContentType), req.Page, req.PageSize)
	} else {
		// 按收藏夹查询
		items, total, err = s.favoriteItemRepo.GetByFolderKSUID(ctx, folderInfo.UserKSUID, req.FavoriteFolderID, req.Page, req.PageSize)
	}

	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folderInfo.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("获取收藏项列表失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 批量获取内容信息
	contentInfoMap, err := s.batchGetContentInfo(ctx, items)
	if err != nil {
		log.Error().Err(err).
			Str("user_ksuid", folderInfo.UserKSUID).
			Str("folder_id", req.FavoriteFolderID).
			Msg("批量获取内容信息失败")
		// 不返回错误，继续处理，只是没有内容详情
	}

	// 转换为响应格式
	var itemResponses []*dto.FavoriteItemResponse
	for _, item := range items {
		itemResponse := s.toItemResponse(item)
		// 添加内容信息
		if contentInfoMap != nil {
			if contentInfo, exists := contentInfoMap[item.ContentKSUID]; exists {
				itemResponse.ContentInfo = &dto.ContentInfo{
					UserKSUID:     contentInfo.UserKSUID,
					ContentKSUID:  contentInfo.ContentKSUID,
					Title:         contentInfo.Title,
					CoverURL:      contentInfo.CoverURL,
					Duration:      contentInfo.Duration,
					ViewCount:     contentInfo.ViewCount,
					LikeCount:     contentInfo.LikeCount,
					CommentCount:  contentInfo.CommentCount,
					FavoriteCount: contentInfo.FavoriteCount,
				}
			}
		}
		itemResponses = append(itemResponses, itemResponse)
	}

	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	response := &dto.GetFavoriteItemsResponse{
		Items:      itemResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	log.Info().
		Str("user_ksuid", folderInfo.UserKSUID).
		Int64("total", total).
		Int("returned", len(itemResponses)).
		Msg("收藏项列表获取成功")

	return response, nil
}

// CheckFavoriteStatus 检查收藏状态
func (s *FavoriteItemService) CheckFavoriteStatus(ctx context.Context, userKSUID string, req *dto.CheckFavoriteStatusRequest) (*dto.CheckFavoriteStatusResponse, *errors.Errors) {
	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Msg("正在检查收藏状态")

	// 获取用户对内容的收藏状态
	items, err := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, req.ContentKSUID)
	if err != nil {
		if err == repositoryErrors.ErrFavoriteItemNotFound {
			// 未收藏
			return &dto.CheckFavoriteStatusResponse{
				IsFavorited: false,
				Folders:     []*dto.FavoriteFolderResponse{},
				Items:       []*dto.FavoriteItemResponse{},
			}, nil
		}
		log.Error().Err(err).
			Str("user_ksuid", userKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("检查收藏状态失败")
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 已收藏，返回收藏信息
	itemResponses := make([]*dto.FavoriteItemResponse, len(items))
	for i, item := range items {
		itemResponses[i] = s.toItemResponse(item)
	}

	response := &dto.CheckFavoriteStatusResponse{
		IsFavorited: len(items) > 0,
		Items:       itemResponses,
	}

	log.Info().
		Str("user_ksuid", userKSUID).
		Str("content_ksuid", req.ContentKSUID).
		Bool("is_favorited", true).
		Msg("收藏状态检查成功")

	return response, nil
}

// ===== 辅助方法 =====

// checkDuplicateFavorites 检查重复收藏
func (s *FavoriteItemService) checkDuplicateFavorites(ctx context.Context, userKSUID, contentKSUID string, folderIDs []string) ([]string, *errors.Errors) {
	// 获取现有的收藏项
	existingItems, err := s.favoriteItemRepo.GetByUserAndContentKSUID(ctx, userKSUID, contentKSUID)
	if err != nil && err != repositoryErrors.ErrFavoriteItemNotFound {
		return nil, errors.NewGlobalErrors(errors.SYSTEM_ERROR, errors.GET_RECORD_ERROR, err)
	}

	// 创建现有收藏夹ID的映射
	existingFolderIDs := make(map[string]bool)
	for _, item := range existingItems {
		existingFolderIDs[item.FavoriteFolderID] = true
	}

	var nonDuplicateIDs []string
	for _, folderID := range folderIDs {
		// 如果这个收藏夹中还没有收藏这个内容，则可以添加
		if !existingFolderIDs[folderID] {
			nonDuplicateIDs = append(nonDuplicateIDs, folderID)
		}
	}

	return nonDuplicateIDs, nil
}

// batchGetContentInfo 批量获取内容信息
func (s *FavoriteItemService) batchGetContentInfo(ctx context.Context, items []*model.FavoriteItem) (map[string]*client.ContentInfo, *errors.Errors) {
	if len(items) == 0 {
		return make(map[string]*client.ContentInfo), nil
	}

	// 按内容类型分组收集内容KSUID
	videoKSUIDs := make([]string, 0)
	novelKSUIDs := make([]string, 0)
	musicKSUIDs := make([]string, 0)

	for _, item := range items {
		switch item.ContentType {
		case "video", "anime", "short":
			videoKSUIDs = append(videoKSUIDs, item.ContentKSUID)
		case "novel":
			novelKSUIDs = append(novelKSUIDs, item.ContentKSUID)
		case "music":
			musicKSUIDs = append(musicKSUIDs, item.ContentKSUID)
		}
	}

	// 合并所有内容信息
	allContentInfo := make(map[string]*client.ContentInfo)

	// 批量获取视频内容信息
	if len(videoKSUIDs) > 0 {
		videoContentInfo, err := s.videoClient.BatchGetContentsByKSUIDs(videoKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("video_ksuids", videoKSUIDs).
				Msg("批量获取视频内容信息失败")
		} else {
			for k, v := range videoContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	// 批量获取小说内容信息
	if len(novelKSUIDs) > 0 {
		novelContentInfo, err := s.novelClient.BatchGetContentsByKSUIDs(novelKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("novel_ksuids", novelKSUIDs).
				Msg("批量获取小说内容信息失败")
		} else {
			for k, v := range novelContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	// 批量获取音乐内容信息
	if len(musicKSUIDs) > 0 {
		musicContentInfo, err := s.musicClient.BatchGetContentsByKSUIDs(musicKSUIDs)
		if err != nil {
			log.Error().Err(err).
				Strs("music_ksuids", musicKSUIDs).
				Msg("批量获取音乐内容信息失败")
		} else {
			for k, v := range musicContentInfo {
				allContentInfo[k] = v
			}
		}
	}

	log.Info().
		Int("total_items", len(items)).
		Int("video_count", len(videoKSUIDs)).
		Int("novel_count", len(novelKSUIDs)).
		Int("music_count", len(musicKSUIDs)).
		Int("content_info_count", len(allContentInfo)).
		Msg("批量获取内容信息完成")

	return allContentInfo, nil
}

// toItemResponse 转换收藏项模型为响应格式
func (s *FavoriteItemService) toItemResponse(item *model.FavoriteItem) *dto.FavoriteItemResponse {
	return &dto.FavoriteItemResponse{
		FavoriteItemID:   item.FavoriteItemID,
		UserKSUID:        item.UserKSUID,
		ContentKSUID:     item.ContentKSUID,
		ContentType:      string(item.ContentType),
		FavoriteFolderID: item.FavoriteFolderID,
		CreatedAt:        item.CreatedAt,
		UpdatedAt:        item.UpdatedAt,
	}
}
