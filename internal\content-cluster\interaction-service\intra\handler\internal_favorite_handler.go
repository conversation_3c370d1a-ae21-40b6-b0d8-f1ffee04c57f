package handler

import (
	"net/http"
	"pxpat-backend/pkg/errors"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"pxpat-backend/internal/content-cluster/interaction-service/intra/service"
	"pxpat-backend/internal/content-cluster/interaction-service/model"
	globalTypes "pxpat-backend/pkg/types"
)

// InternalFavoriteHandler 内部收藏处理器
type InternalFavoriteHandler struct {
	internalFavoriteService *service.InternalFavoriteService
}

// NewInternalFavoriteHandler 创建内部收藏处理器
func NewInternalFavoriteHandler(internalFavoriteService *service.InternalFavoriteService) *InternalFavoriteHandler {
	return &InternalFavoriteHandler{
		internalFavoriteService: internalFavoriteService,
	}
}

// AddToFavoriteInternalRequest 内部添加收藏请求
type AddToFavoriteInternalRequest struct {
	UserKSUID    string `json:"user_ksuid" binding:"required"`
	ContentKSUID string `json:"content_ksuid" binding:"required"`
	ContentType  string `json:"content_type" binding:"required"`
	FolderID     string `json:"folder_id,omitempty"`
}

// RemoveFromFavoriteInternalRequest 内部移除收藏请求
type RemoveFromFavoriteInternalRequest struct {
	UserKSUID    string `json:"user_ksuid" binding:"required"`
	ContentKSUID string `json:"content_ksuid" binding:"required"`
	FolderID     string `json:"folder_id,omitempty"`
}

// CheckFavoriteStatusInternalRequest 内部检查收藏状态请求
type CheckFavoriteStatusInternalRequest struct {
	UserKSUID    string `json:"user_ksuid" binding:"required"`
	ContentKSUID string `json:"content_ksuid" binding:"required"`
}

// BatchCheckFavoriteStatusInternalRequest 内部批量检查收藏状态请求
type BatchCheckFavoriteStatusInternalRequest struct {
	UserKSUID     string   `json:"user_ksuid" binding:"required"`
	ContentKSUIDs []string `json:"content_ksuids" binding:"required"`
}

// AddToFavoriteInternal 内部添加到收藏夹
func (h *InternalFavoriteHandler) AddToFavoriteInternal(c *gin.Context) {
	var req AddToFavoriteInternalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("内部添加收藏请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	// 验证内容类型
	contentType := model.ContentType(req.ContentType)
	if !model.IsValidContentType(contentType) {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	gErr := h.internalFavoriteService.AddToFavoriteInternal(
		c.Request.Context(),
		req.UserKSUID,
		req.ContentKSUID,
		contentType,
		req.FolderID,
	)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内部添加收藏失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
	})
}

// RemoveFromFavoriteInternal 内部从收藏夹移除
func (h *InternalFavoriteHandler) RemoveFromFavoriteInternal(c *gin.Context) {
	var req RemoveFromFavoriteInternalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("内部移除收藏请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	gErr := h.internalFavoriteService.RemoveFromFavoriteInternal(
		c.Request.Context(),
		req.UserKSUID,
		req.ContentKSUID,
		req.FolderID,
	)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内部移除收藏失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.SUCCESS,
	})
}

// CheckFavoriteStatusInternal 内部检查收藏状态
func (h *InternalFavoriteHandler) CheckFavoriteStatusInternal(c *gin.Context) {
	var req CheckFavoriteStatusInternalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("内部检查收藏状态请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	isFavorited, folderIDs, gErr := h.internalFavoriteService.CheckFavoriteStatusInternal(
		c.Request.Context(),
		req.UserKSUID,
		req.ContentKSUID,
	)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", req.UserKSUID).
			Str("content_ksuid", req.ContentKSUID).
			Msg("内部检查收藏状态失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: map[string]interface{}{
			"is_favorited": isFavorited,
			"folder_ids":   folderIDs,
		},
	})
}

// GetUserFavoriteStatsInternal 内部获取用户收藏统计
func (h *InternalFavoriteHandler) GetUserFavoriteStatsInternal(c *gin.Context) {
	userKSUID := c.Param("user_ksuid")
	if userKSUID == "" {
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	stats, gErr := h.internalFavoriteService.GetUserFavoriteStatsInternal(c.Request.Context(), userKSUID)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", userKSUID).
			Msg("内部获取用户收藏统计失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: stats,
	})
}

// BatchCheckFavoriteStatusInternal 内部批量检查收藏状态
func (h *InternalFavoriteHandler) BatchCheckFavoriteStatusInternal(c *gin.Context) {
	var req BatchCheckFavoriteStatusInternalRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Error().Err(err).Msg("内部批量检查收藏状态请求参数无效")
		c.JSON(http.StatusBadRequest, globalTypes.GlobalResponse{
			Code: errors.INVALID_PARAMETER,
		})
		return
	}

	favoriteMap, gErr := h.internalFavoriteService.BatchCheckFavoriteStatusInternal(
		c.Request.Context(),
		req.UserKSUID,
		req.ContentKSUIDs,
	)
	if gErr != nil {
		log.Error().Err(gErr).
			Str("user_ksuid", req.UserKSUID).
			Msg("内部批量检查收藏状态失败")
		c.JSON(http.StatusInternalServerError, globalTypes.GlobalResponse{
			Code: gErr.Code,
		})
		return
	}

	c.JSON(http.StatusOK, globalTypes.GlobalResponse{
		Code: errors.HAVE_DATA_SUCCESS,
		Data: favoriteMap,
	})
}
